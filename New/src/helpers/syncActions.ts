/**
 * Sync Action Execution Module for DermaCare Bi-Directional Sync Service
 *
 * This module provides execution functions for context-aware sync operations.
 * It implements the actual API calls and database updates based on the sync
 * actions determined by the contextAwareSync module.
 *
 * **Key Features:**
 * - Executes context-aware sync operations (create, update, search-then-create)
 * - Handles both patient and appointment sync scenarios
 * - Implements search-then-create logic with proper fallback handling
 * - Maintains comprehensive error handling and logging
 * - Preserves existing functionality while adding intelligence
 *
 * **Execution Flow:**
 * 1. Receive sync context from contextAwareSync module
 * 2. Execute appropriate API calls based on determined action
 * 3. Update local database with results
 * 4. Return standardized response with success/failure status
 *
 * @example
 * ```typescript
 * // Execute patient sync based on determined action
 * const context = determinePatientSyncAction(dbPatient, 'cc', 'create');
 * const result = await executePatientSync(dbPatient, payload, context.action);
 * 
 * if (result.success) {
 *   console.log(`Sync completed: ${result.message}`);
 * }
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import { getDb } from "@database";
import { patient } from "@database/schema";
import { searchCreateOrUpdatePatient } from "@storage/patientStorage";
import { syncCCtoAPCustomFields, syncAPtoCCCustomFields } from "@storage/customFieldSync";
import { logSyncError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import { apClient } from "../api/apClient";
import { ccClient } from "../api";
import { transformAPContactToCCPatient } from "../helpers/dataTransform";
import type {
	GetCCPatientType,
	GetAPContactType,
	GetCCAppointmentType,
	GetAPAppointmentType,
} from "@type";
import type {
	SyncAction,
	SyncPlatform,
	PatientRecord,
	AppointmentRecord,
} from "./contextAwareSync";

/**
 * Standard sync operation result interface
 */
export interface SyncResult {
	/** Whether the sync operation was successful */
	success: boolean;
	/** Human-readable message describing the result */
	message: string;
	/** Optional ID of the created/updated record */
	recordId?: string | number;
	/** Whether the operation was skipped */
	skipped?: boolean;
	/** Any additional data from the sync operation */
	data?: Record<string, unknown>;
}

/**
 * Executes patient sync operations based on determined context-aware action
 *
 * This function handles the actual execution of patient synchronization between
 * CC and AP platforms. It implements the logic for create, update, and
 * search-then-create operations while maintaining proper error handling.
 *
 * **Supported Actions:**
 * - `skip`: Returns success without making API calls
 * - `create`: Creates new patient/contact in target platform
 * - `update`: Updates existing patient/contact in target platform
 * - `search_then_create`: Searches target platform first, creates if not found
 *
 * **Error Handling:**
 * - Logs sync errors to database for monitoring
 * - Returns detailed error messages for debugging
 * - Preserves database consistency on failures
 *
 * @param dbPatient - Patient record from local database
 * @param payload - Webhook payload data (CC patient or AP contact)
 * @param action - Determined sync action to execute
 * @param targetPlatform - Target platform for sync ('cc' or 'ap')
 * @returns Promise resolving to sync operation result
 *
 * @example
 * ```typescript
 * // Execute CC → AP patient sync
 * const result = await executePatientSync(
 *   dbPatient,
 *   ccPatientPayload,
 *   'create',
 *   'ap'
 * );
 *
 * if (result.success) {
 *   console.log(`Patient synced to AP: ${result.recordId}`);
 * } else {
 *   console.error(`Sync failed: ${result.message}`);
 * }
 * ```
 */
export async function executePatientSync(
	dbPatient: PatientRecord,
	payload: GetCCPatientType | GetAPContactType,
	action: SyncAction,
	targetPlatform: SyncPlatform,
): Promise<SyncResult> {
	try {
		// Handle skip action
		if (action === 'skip') {
			const targetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
			return {
				success: true,
				message: `Patient already synced to ${targetPlatform.toUpperCase()}. ${targetIdField}: ${dbPatient[targetIdField]}`,
				skipped: true,
				recordId: dbPatient[targetIdField] || undefined,
			};
		}

		// Execute platform-specific sync
		if (targetPlatform === 'ap') {
			return await executeCCtoAPSync(dbPatient, payload as GetCCPatientType, action);
		} else {
			return await executeAPtoCCSync(dbPatient, payload as GetAPContactType, action);
		}
	} catch (error) {
		const errorMessage = `Failed to execute patient sync: ${error instanceof Error ? error.message : 'Unknown error'}`;
		console.error(errorMessage, error);
		
		await logSyncError(
			"PATIENT_SYNC_EXECUTION_FAILED",
			errorMessage,
			dbPatient.id,
			targetPlatform.toUpperCase(),
			"SyncActions",
		);

		return {
			success: false,
			message: errorMessage,
		};
	}
}

/**
 * Executes CC → AP patient synchronization
 *
 * @param dbPatient - Local patient record
 * @param payload - CC patient data
 * @param action - Sync action to execute
 * @returns Sync operation result
 */
async function executeCCtoAPSync(
	dbPatient: PatientRecord,
	payload: GetCCPatientType,
	action: SyncAction,
): Promise<SyncResult> {
	try {
		let apContact: GetAPContactType | null = null;

		if (action === 'update' && dbPatient.apId) {
			// Update existing AP contact
			const updateData = {
				email: payload.email || dbPatient.email,
				phone: payload.phoneMobile || dbPatient.phone,
				firstName: payload.firstName,
				lastName: payload.lastName,
				dateOfBirth: payload.dob,
				gender: payload.gender,
			};

			apContact = await apClient.contact.update(dbPatient.apId, updateData);
			
			if (!apContact) {
				throw new Error(`Failed to update AP contact: ${dbPatient.apId}`);
			}

		} else if (action === 'create' || action === 'search_then_create') {
			// Search for existing contact first if search_then_create
			if (action === 'search_then_create') {
				// Try to find existing contact by email or phone using the all method with query
				let searchResults: GetAPContactType[] = [];

				if (payload.email) {
					const emailResults = await apClient.contact.all({
						query: payload.email,
						limit: 10,
					});
					searchResults = emailResults.filter(contact =>
						contact.email?.toLowerCase() === payload.email?.toLowerCase()
					);
				}

				// If no email match and we have phone, search by phone
				if (searchResults.length === 0 && payload.phoneMobile) {
					const phoneResults = await apClient.contact.all({
						query: payload.phoneMobile,
						limit: 10,
					});
					searchResults = phoneResults.filter(contact =>
						contact.phone === payload.phoneMobile
					);
				}

				if (searchResults.length > 0) {
					// Found existing contact, update it
					apContact = await apClient.contact.update(searchResults[0].id, {
						email: payload.email,
						phone: payload.phoneMobile,
						firstName: payload.firstName,
						lastName: payload.lastName,
						dateOfBirth: payload.dob,
						gender: payload.gender,
					});
				}
			}

			// Create new contact if not found
			if (!apContact) {
				const createData = {
					email: payload.email,
					phone: payload.phoneMobile,
					firstName: payload.firstName,
					lastName: payload.lastName,
					dateOfBirth: payload.dob,
					gender: payload.gender,
					source: 'cc',
					tags: ['cc_api'],
				};

				apContact = await apClient.contact.create(createData);
			}

			if (!apContact) {
				throw new Error('Failed to create AP contact');
			}
		}

		// Update local database with AP data
		if (apContact) {
			await searchCreateOrUpdatePatient({
				ccId: payload.id,
				apId: apContact.id,
				ccData: payload,
				apData: apContact,
				email: payload.email || undefined,
				phone: payload.phoneMobile || undefined,
				source: "cc",
			});

			// Sync custom fields
			try {
				await syncCCtoAPCustomFields(dbPatient, payload);
			} catch (customFieldError) {
				console.error(`Custom field sync failed:`, customFieldError);
				// Don't fail the entire operation for custom field errors
			}

			return {
				success: true,
				message: `Patient successfully synced to AP. AP ID: ${apContact.id}`,
				recordId: apContact.id,
				data: { apContact },
			};
		}

		throw new Error('No AP contact result after sync operation');

	} catch (error) {
		const errorMessage = `CC to AP sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
		
		await logSyncError(
			"CC_TO_AP_PATIENT_SYNC_FAILED",
			errorMessage,
			payload.id,
			"AP",
			"SyncActions",
		);

		return {
			success: false,
			message: errorMessage,
		};
	}
}

/**
 * Executes AP → CC patient synchronization
 *
 * @param dbPatient - Local patient record
 * @param payload - AP contact data
 * @param action - Sync action to execute
 * @returns Sync operation result
 */
async function executeAPtoCCSync(
	dbPatient: PatientRecord,
	payload: GetAPContactType,
	action: SyncAction,
): Promise<SyncResult> {
	try {
		let ccPatient: GetCCPatientType | null = null;
		const ccPatientData = transformAPContactToCCPatient(payload);

		if (action === 'update' && dbPatient.ccId) {
			// Update existing CC patient
			ccPatient = await ccClient.patient.update(dbPatient.ccId, ccPatientData);
			
			if (!ccPatient) {
				throw new Error(`Failed to update CC patient: ${dbPatient.ccId}`);
			}

		} else if (action === 'create' || action === 'search_then_create') {
			// Search for existing patient first if search_then_create
			if (action === 'search_then_create') {
				// Try to find existing patient by email or phone
				const searchResults = await ccClient.patient.search({
					email: payload.email,
					phone: payload.phone,
				});

				if (searchResults && searchResults.length > 0) {
					// Found existing patient, update it
					ccPatient = await ccClient.patient.update(searchResults[0].id, ccPatientData);
				}
			}

			// Create new patient if not found
			if (!ccPatient) {
				ccPatient = await ccClient.patient.create(ccPatientData);
			}

			if (!ccPatient) {
				throw new Error('Failed to create CC patient');
			}
		}

		// Update local database with CC data
		if (ccPatient) {
			await searchCreateOrUpdatePatient({
				apId: payload.id,
				ccId: ccPatient.id,
				apData: payload,
				ccData: ccPatient,
				email: payload.email || undefined,
				phone: payload.phone || undefined,
				source: "ap",
			});

			// Sync custom fields
			try {
				await syncAPtoCCCustomFields(dbPatient, payload);
			} catch (customFieldError) {
				console.error(`Custom field sync failed:`, customFieldError);
				// Don't fail the entire operation for custom field errors
			}

			return {
				success: true,
				message: `Contact successfully synced to CC. CC ID: ${ccPatient.id}`,
				recordId: ccPatient.id,
				data: { ccPatient },
			};
		}

		throw new Error('No CC patient result after sync operation');

	} catch (error) {
		const errorMessage = `AP to CC sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
		
		await logSyncError(
			"AP_TO_CC_PATIENT_SYNC_FAILED",
			errorMessage,
			payload.id,
			"CC",
			"SyncActions",
		);

		return {
			success: false,
			message: errorMessage,
		};
	}
}

/**
 * Executes appointment sync operations based on determined context-aware action
 *
 * This function handles appointment synchronization between CC and AP platforms,
 * ensuring that the associated patient is properly synced before attempting
 * appointment operations.
 *
 * **Prerequisites:**
 * - Patient must be synced to target platform
 * - Patient must have valid target platform ID
 *
 * @param dbPatient - Patient record from local database
 * @param dbAppointment - Appointment record from local database (null if creating)
 * @param payload - Webhook payload data (CC appointment or AP appointment)
 * @param action - Determined sync action to execute
 * @param targetPlatform - Target platform for sync ('cc' or 'ap')
 * @returns Promise resolving to sync operation result
 *
 * @example
 * ```typescript
 * // Execute CC → AP appointment sync
 * const result = await executeAppointmentSync(
 *   dbPatient,
 *   dbAppointment,
 *   ccAppointmentPayload,
 *   'create',
 *   'ap'
 * );
 * ```
 */
export async function executeAppointmentSync(
	dbPatient: PatientRecord,
	dbAppointment: AppointmentRecord | null,
	payload: GetCCAppointmentType | GetAPAppointmentType,
	action: SyncAction,
	targetPlatform: SyncPlatform,
): Promise<SyncResult> {
	try {
		// Validate patient is synced to target platform
		const patientTargetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
		const patientTargetId = dbPatient[patientTargetIdField];

		if (!patientTargetId) {
			return {
				success: false,
				message: `Cannot sync appointment: patient not synced to ${targetPlatform.toUpperCase()}. Patient ${patientTargetIdField}: ${patientTargetId}`,
			};
		}

		// Handle skip action
		if (action === 'skip') {
			const appointmentTargetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
			const appointmentTargetId = dbAppointment?.[appointmentTargetIdField];
			return {
				success: true,
				message: `Appointment already synced to ${targetPlatform.toUpperCase()}. ${appointmentTargetIdField}: ${appointmentTargetId}`,
				skipped: true,
				recordId: appointmentTargetId || undefined,
			};
		}

		// Execute platform-specific appointment sync
		if (targetPlatform === 'ap') {
			return await executeCCtoAPAppointmentSync(
				dbPatient,
				dbAppointment,
				payload as GetCCAppointmentType,
				action,
			);
		} else {
			return await executeAPtoCCAppointmentSync(
				dbPatient,
				dbAppointment,
				payload as GetAPAppointmentType,
				action,
			);
		}
	} catch (error) {
		const errorMessage = `Failed to execute appointment sync: ${error instanceof Error ? error.message : 'Unknown error'}`;
		console.error(errorMessage, error);

		await logSyncError(
			"APPOINTMENT_SYNC_EXECUTION_FAILED",
			errorMessage,
			dbPatient.id,
			targetPlatform.toUpperCase(),
			"SyncActions",
		);

		return {
			success: false,
			message: errorMessage,
		};
	}
}

/**
 * Executes CC → AP appointment synchronization
 *
 * @param dbPatient - Local patient record
 * @param dbAppointment - Local appointment record (null if creating)
 * @param payload - CC appointment data
 * @param action - Sync action to execute
 * @returns Sync operation result
 */
async function executeCCtoAPAppointmentSync(
	dbPatient: PatientRecord,
	dbAppointment: AppointmentRecord | null,
	payload: GetCCAppointmentType,
	action: SyncAction,
): Promise<SyncResult> {
	try {
		// Import the createAPAppointment function
		const { createAPAppointment } = await import("../processors/appointmentProcessor");

		if (action === 'skip') {
			return {
				success: true,
				message: `Appointment already synced to AP. AP ID: ${dbAppointment?.apId}`,
				skipped: true,
				recordId: dbAppointment?.apId || undefined,
			};
		}

		// Create appointment in AP using existing logic
		const apAppointmentResult = await createAPAppointment(dbPatient.apId!, payload);

		if (apAppointmentResult.success && apAppointmentResult.apAppointment) {
			return {
				success: true,
				message: `Appointment successfully synced to AP. AP ID: ${apAppointmentResult.apAppointment.id}`,
				recordId: apAppointmentResult.apAppointment.id,
				data: { apAppointment: apAppointmentResult.apAppointment },
			};
		} else {
			throw new Error(apAppointmentResult.message);
		}

	} catch (error) {
		const errorMessage = `CC to AP appointment sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;

		await logSyncError(
			"CC_TO_AP_APPOINTMENT_SYNC_FAILED",
			errorMessage,
			payload.id,
			"AP",
			"SyncActions",
		);

		return {
			success: false,
			message: errorMessage,
		};
	}
}

/**
 * Executes AP → CC appointment synchronization
 *
 * @param dbPatient - Local patient record
 * @param dbAppointment - Local appointment record (null if creating)
 * @param payload - AP appointment data
 * @param action - Sync action to execute
 * @returns Sync operation result
 */
async function executeAPtoCCAppointmentSync(
	dbPatient: PatientRecord,
	dbAppointment: AppointmentRecord | null,
	payload: GetAPAppointmentType,
	action: SyncAction,
): Promise<SyncResult> {
	try {
		// Import the createCCAppointment function
		const { createCCAppointment } = await import("../processors/apAppointmentProcessor");

		if (action === 'skip') {
			return {
				success: true,
				message: `Appointment already synced to CC. CC ID: ${dbAppointment?.ccId}`,
				skipped: true,
				recordId: dbAppointment?.ccId || undefined,
			};
		}

		// Create appointment in CC using existing logic
		const ccAppointmentResult = await createCCAppointment(dbPatient.ccId!, payload);

		if (ccAppointmentResult.success && ccAppointmentResult.ccAppointment) {
			return {
				success: true,
				message: `Appointment successfully synced to CC. CC ID: ${ccAppointmentResult.ccAppointment.id}`,
				recordId: ccAppointmentResult.ccAppointment.id,
				data: { ccAppointment: ccAppointmentResult.ccAppointment },
			};
		} else {
			throw new Error(ccAppointmentResult.message);
		}

	} catch (error) {
		const errorMessage = `AP to CC appointment sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;

		await logSyncError(
			"AP_TO_CC_APPOINTMENT_SYNC_FAILED",
			errorMessage,
			payload.id,
			"CC",
			"SyncActions",
		);

		return {
			success: false,
			message: errorMessage,
		};
	}
}
