import { getDb } from "@database";
import { patient } from "@database/schema";
import { searchCreateOrUpdatePatient } from "@storage/patientStorage";
import { syncAPtoCCCustomFields } from "@storage/customFieldSync";
import type { GetAPContactType, WebhookContext } from "@type";
import {
	checkAndAddToBuffer,
	generatePatientBufferKey,
} from "@utils/bufferManager";
import { logError, logSyncError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import { ccClient } from "../api";
import { transformAPContactToCCPatient } from "../helpers/dataTransform";
import { determinePatientSyncAction } from "../helpers/contextAwareSync";
import { executePatientSync } from "../helpers/syncActions";

/**
 * Processes AP contact creation events and syncs to CC as patients
 *
 * This function handles the AP → CC data flow when new contacts are created in AutoPatient.
 * It transforms AP contact data to CC patient format and creates corresponding patient
 * records in CliniCore, maintaining bi-directional synchronization between platforms.
 *
 * **Business Logic Flow (Context-Aware):**
 * 1. Check buffer to prevent duplicate processing of the same contact
 * 2. Early database check: Verify if contact already exists in local database
 * 3. Context-aware decision: Determine if contact needs CC sync based on existing state
 * 4. Smart API execution: Skip, create, update, or search-then-create in CC platform
 * 5. Store/update local database record with both AP and CC IDs
 * 6. Handle any errors with comprehensive logging
 *
 * **Data Transformation:**
 * - Maps AP contact fields to CC patient fields
 * - Handles custom field synchronization
 * - Preserves contact metadata and timestamps
 * - Maintains source tracking for audit purposes
 *
 * **Error Handling:**
 * - Validates required contact data before processing
 * - Handles API failures with retry logic
 * - Logs detailed error information for debugging
 * - Returns appropriate success/failure status
 *
 * **Performance Considerations:**
 * - Uses buffer management to prevent duplicate processing
 * - Optimized database queries with proper indexing
 * - Efficient API calls with minimal data transfer
 * - Designed to complete within webhook timeout limits
 *
 * @param payload - AP contact data from webhook event
 * @param payload.id - Unique AP contact identifier
 * @param payload.firstName - Contact's first name
 * @param payload.lastName - Contact's last name
 * @param payload.email - Contact's email address
 * @param payload.phone - Contact's phone number
 * @param payload.customFields - Array of custom field values
 * @param payload.dateAdded - Contact creation timestamp
 * @param payload.dateUpdated - Last update timestamp
 *
 * @param context - Webhook processing context with metadata
 * @param context.event - Original webhook event data
 * @param context.processedAt - Processing timestamp
 * @param context.requestId - Unique request identifier for tracking
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if processing succeeded
 * @returns result.message - Human-readable processing message
 * @returns result.skipped - Boolean indicating if processing was skipped (duplicate)
 * @returns result.patientId - CC patient ID if creation was successful
 * @returns result.contactId - AP contact ID for reference
 *
 * @throws {Error} Critical errors are caught and logged, function returns error result
 *
 * @example
 * ```typescript
 * // Example AP contact webhook payload
 * const apContactPayload = {
 *   id: "ap_contact_123",
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phone: "+**********",
 *   dateAdded: "2024-01-01T12:00:00Z",
 *   customFields: [
 *     { id: "field_1", value: "Custom Value" }
 *   ]
 * };
 *
 * const context = {
 *   event: webhookEvent,
 *   processedAt: new Date(),
 *   requestId: "req_abc123"
 * };
 *
 * const result = await processAPContactCreate(apContactPayload, context);
 *
 * // Example success result
 * {
 *   success: true,
 *   message: "Contact synced to CC successfully",
 *   patientId: 12345,
 *   contactId: "ap_contact_123"
 * }
 * ```
 *
 * @see {@link transformAPContactToCCPatient} for data transformation logic
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 * @see {@link checkAndAddToBuffer} for duplicate prevention
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processAPContactCreate(
	payload: GetAPContactType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP contact create for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessAPContactCreate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP contact creation recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Validate required data
		if (!payload.email && !payload.phone) {
			const message = `Email and phone are empty, cannot create patient. AP Contact ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Use smart patient upsert logic (equivalent to v3Integration's Contact.searchCreateOrUpdate)
		const dbPatient = await searchCreateOrUpdatePatient({
			apId: payload.id,
			apData: payload,
			email: payload.email || undefined,
			phone: payload.phone || undefined,
			source: "ap",
		});

		// NEW: Determine sync action based on context-aware logic
		const syncContext = determinePatientSyncAction(dbPatient, 'ap', 'create');
		console.log(`🎯 Context-aware sync decision: ${syncContext.action} - ${syncContext.reason}`);

		// Handle skip action (contact already synced)
		if (syncContext.action === 'skip') {
			return {
				success: true,
				message: syncContext.reason,
			};
		}

		// Execute context-aware patient sync
		console.log(`🔄 Executing context-aware patient sync for AP ID: ${payload.id}`);
		const syncResult = await executePatientSync(dbPatient, payload, syncContext.action, 'cc');
		console.log(`🔄 Context-aware sync result:`, syncResult);

		// Handle sync result
		if (syncResult.success) {
			console.log(
				`Contact sync completed successfully. AP ID: ${payload.id}, Result: ${syncResult.message}`,
			);

			return {
				success: true,
				message: syncResult.message,
			};
		} else {
			await logSyncError(
				"AP_CONTACT_CREATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"AP",
				"APContactProcessor",
			);

			return {
				success: false,
				message: `Failed to sync contact to CC: ${syncResult.message}`,
			};
		}
	} catch (error) {
		const message = `Error processing AP contact create: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_CONTACT_CREATE_ERROR",
			error,
			{
				apContactId: payload.id,
				requestId: context.requestId,
			},
			"APContactProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Processes AP contact update events and syncs to CC patients
 * This handles AP → CC data flow
 *
 * @param payload - AP contact data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPContactUpdate(
	payload: GetAPContactType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP contact update for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessAPContactUpdate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP contact update recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Validate required data
		if (!payload.email && !payload.phone) {
			const message = `Email and phone are empty, dropping update request. AP Contact ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Use smart patient upsert logic (equivalent to v3Integration's Contact.searchCreateOrUpdate)
		const dbPatient = await searchCreateOrUpdatePatient({
			apId: payload.id,
			apData: payload,
			email: payload.email || undefined,
			phone: payload.phone || undefined,
			source: "ap",
		});

		// NEW: Determine sync action based on context-aware logic
		const syncContext = determinePatientSyncAction(dbPatient, 'ap', 'update');
		console.log(`🎯 Context-aware sync decision: ${syncContext.action} - ${syncContext.reason}`);

		// Execute context-aware patient sync
		console.log(`🔄 Executing context-aware patient sync for AP ID: ${payload.id}`);
		const syncResult = await executePatientSync(dbPatient, payload, syncContext.action, 'cc');
		console.log(`🔄 Context-aware sync result:`, syncResult);

		// Handle sync result
		if (syncResult.success) {
			console.log(
				`Contact sync completed successfully. AP ID: ${payload.id}, Result: ${syncResult.message}`,
			);

			return {
				success: true,
				message: syncResult.message,
			};
		} else {
			await logSyncError(
				"AP_CONTACT_UPDATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"AP",
				"APContactProcessor",
			);

			return {
				success: false,
				message: `Failed to sync contact to CC: ${syncResult.message}`,
			};
		}
	} catch (error) {
		const message = `Error processing AP contact update: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_CONTACT_UPDATE_ERROR",
			error,
			{
				apContactId: payload.id,
				requestId: context.requestId,
			},
			"APContactProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Processes AP contact deletion events and syncs to CC patients
 * This handles AP → CC data flow
 *
 * @param payload - AP contact data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPContactDelete(
	payload: GetAPContactType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP contact delete for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessAPContactDelete",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP contact deletion recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Find patient in database
		const dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.apId, payload.id))
			.limit(1)
			.then((results) => results[0]);

		if (!dbPatient) {
			console.log(
				`Patient not found for deletion, AP contact ID: ${payload.id}`,
			);
			return {
				success: true,
				message: `Patient not found, nothing to delete. AP Contact ID: ${payload.id}`,
			};
		}

		// Note: We typically don't delete patients from CC when AP contacts are deleted
		// Instead, we just remove the AP association
		console.log(
			`Removing AP association for patient. CC ID: ${dbPatient.ccId}`,
		);

		// Update patient in database to remove AP association
		await db
			.update(patient)
			.set({
				apId: null,
				apData: null,
				apUpdatedAt: null,
			})
			.where(eq(patient.id, dbPatient.id));

		return {
			success: true,
			message: `AP association removed from patient. CC ID: ${dbPatient.ccId}`,
		};
	} catch (error) {
		const message = `Error processing AP contact delete: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_CONTACT_DELETE_ERROR",
			error,
			{
				apContactId: payload.id,
				requestId: context.requestId,
			},
			"APContactProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}
