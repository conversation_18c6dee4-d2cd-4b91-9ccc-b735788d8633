import { searchCreateOrUpdatePatient } from "@storage/patientStorage";
import type { GetCCPatientType, WebhookContext } from "@type";
import {
	checkAndAddToBuffer,
	generatePatientBuffer<PERSON>ey,
} from "@utils/bufferManager";
import { logSyncError } from "@utils/errorLogger";
import { determinePatientSyncAction } from "../helpers/contextAwareSync";
import { executePatientSync } from "../helpers/syncActions";

/**
 * Processes patient creation events from CC (CliniCore)
 *
 * This function handles the creation of new patients in the CC system and synchronizes
 * them to the AP (AutoPatient) platform. It implements the same business logic as the
 * ProcessPatientCreate job from v3Integration but with improved error handling and
 * performance optimizations.
 *
 * **Business Logic Flow (Context-Aware):**
 * 1. Check buffer to prevent duplicate processing
 * 2. Validate required patient data (email or phone must be present)
 * 3. Early database check: Create or update local patient record with CC data
 * 4. Context-aware decision: Determine if patient needs AP sync based on existing state
 * 5. Smart API execution: Skip, create, update, or search-then-create in AP platform
 * 6. Handle custom field synchronization
 * 7. Log any errors for monitoring and debugging
 *
 * **Buffer Management:**
 * Uses timestamp-based buffer to prevent duplicate processing of the same patient
 * creation event within the configured buffer time window (default: 60 seconds).
 *
 * **Error Handling:**
 * - Validates email/phone requirements before processing
 * - Logs sync errors to database for monitoring
 * - Returns detailed error messages for debugging
 * - Gracefully handles API failures and database errors
 *
 * @param payload - Complete patient data from CC webhook event
 * @param payload.id - Unique CC patient ID
 * @param payload.email - Patient email address (required if phone is empty)
 * @param payload.phoneMobile - Patient mobile phone (required if email is empty)
 * @param payload.firstName - Patient first name
 * @param payload.lastName - Patient last name
 * @param payload.updatedAt - Timestamp of last update in CC
 * @param context - Webhook processing context containing request metadata
 * @param context.requestId - Unique request identifier for tracking
 * @param context.processedAt - Timestamp when processing started
 * @param context.event - Original webhook event data
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if processing was successful
 * @returns result.message - Descriptive message about the processing outcome
 * @returns result.patientId - AP contact ID if creation was successful
 *
 * @throws {Error} When critical errors occur that prevent processing
 *
 * @example
 * ```typescript
* const webhookPayload = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phoneMobile: "+**********",
 *   updatedAt: "2024-01-01T12:00:00Z"
 * };
 *
 * const context = {
 *   requestId: "req_123",
 *   processedAt: new Date(),
 *   event: originalWebhookEvent
 * };
 *
 * const result = await processPatientCreate(webhookPayload, context);
 *
 * if (result.success) {
 *   console.log(`Patient created successfully: ${result.patientId}`);
 * } else {
 *   console.error(`Patient creation failed: ${result.message}`);
 * }
 *
```
 *
 * @see {@link processPatientUpdate} for handling patient updates
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 * @see {@link generatePatientBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processPatientCreate(
	payload: GetCCPatientType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; patientId?: string }> {
	try {
		console.log(`Processing patient create for CC ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessPatientCreate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Patient creation recently processed, skipping. CC ID: ${payload.id}`,
			};
		}

		// Validate required data
		if (!payload?.email && !payload?.phoneMobile) {
			const message = `Email and phone are empty, dropping create patient request. CC ID: ${payload?.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Use smart patient upsert logic (equivalent to v3Integration's Contact.searchCreateOrUpdate)
		const dbPatient = await searchCreateOrUpdatePatient({
			ccId: payload.id,
			ccData: payload,
			email: payload.email || undefined,
			phone: payload.phoneMobile || undefined,
			source: "cc",
		});

		// NEW: Determine sync action based on context-aware logic
		const syncContext = determinePatientSyncAction(dbPatient, "cc", "create");
		console.log(
			`🎯 Context-aware sync decision: ${syncContext.action} - ${syncContext.reason}`,
		);

		// Handle skip action (patient already synced)
		if (syncContext.action === "skip") {
			return {
				success: true,
				message: syncContext.reason,
				patientId: dbPatient.apId || undefined,
			};
		}

		// Execute context-aware patient sync
		console.log(
			`🔄 Executing context-aware patient sync for CC ID: ${payload.id}`,
		);
		const syncResult = await executePatientSync(
			dbPatient,
			payload,
			syncContext.action,
			"ap",
		);
		console.log(`🔄 Context-aware sync result:`, syncResult);

		// Handle sync result
		if (syncResult.success) {
			console.log(
				`Patient sync completed successfully. CC ID: ${payload.id}, Result: ${syncResult.message}`,
			);

			return {
				success: true,
				message: syncResult.message,
				patientId: syncResult.recordId?.toString(),
			};
		} else {
			await logSyncError(
				"PATIENT_CREATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"CC",
				"PatientProcessor",
			);

			return {
				success: false,
				message: `Failed to sync patient to AP: ${syncResult.message}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"PATIENT_CREATE_ERROR",
			error,
			payload.id,
			"CC",
			"PatientProcessor",
		);

		throw error;
	}
}

/**
 * Processes patient update events from CC (CliniCore)
 *
 * This function handles updates to existing patients in the CC system and synchronizes
 * the changes to the AP (AutoPatient) platform. It implements the same business logic
 * as the ProcessPatientUpdate job from v3Integration with enhanced error handling.
 *
 * **Business Logic Flow (Context-Aware):**
 * 1. Check buffer to prevent duplicate processing of the same update
 * 2. Validate required patient data (email or phone must be present)
 * 3. Early database check: Find existing patient in local database or create if not found
 * 4. Update local patient record with latest CC data
 * 5. Context-aware decision: Determine if patient needs AP sync (update vs search-then-create)
 * 6. Smart API execution: Update existing AP contact or search-then-create if not synced
 * 7. Synchronize custom fields between platforms
 * 8. Handle any errors and log for monitoring
 *
 * **Update vs Create Logic:**
 * If the patient doesn't exist locally, this function will automatically
 * delegate to processPatientCreate() to handle the initial creation.
 *
 * **Data Validation:**
 * - Requires either email or phone to be present (not both empty)
 * - Validates CC patient ID exists
 * - Ensures data integrity before AP synchronization
 *
 * **Performance Considerations:**
 * - Uses buffer management to prevent duplicate processing
 * - Optimized database queries with proper indexing
 * - Efficient AP API calls with error retry logic
 *
 * @param payload - Updated patient data from CC webhook event
 * @param payload.id - Unique CC patient ID
 * @param payload.email - Updated patient email address
 * @param payload.phoneMobile - Updated patient mobile phone
 * @param payload.firstName - Updated patient first name
 * @param payload.lastName - Updated patient last name
 * @param payload.updatedAt - Timestamp of the update in CC
 * @param context - Webhook processing context
 * @param context.requestId - Unique request identifier for tracking
 * @param context.processedAt - Timestamp when processing started
 * @param context.event - Original webhook event data
 *
 * @returns Promise resolving to processing result object
 * @returns result.success - Boolean indicating if update was successful
 * @returns result.message - Descriptive message about the update outcome
 * @returns result.patientId - AP contact ID if update was successful
 *
 * @throws {Error} When critical errors occur that prevent processing
 *
 * @example
 * ```typescript
* const updatePayload = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Smith", // Updated last name
 *   email: "<EMAIL>", // Updated email
 *   phoneMobile: "+**********",
 *   updatedAt: "2024-01-02T12:00:00Z"
 * };
 *
 * const result = await processPatientUpdate(updatePayload, context);
 *
 * if (result.success) {
 *   console.log(`Patient updated successfully: ${result.patientId}`);
 * } else {
 *   console.error(`Patient update failed: ${result.message}`);
 * }
 *
```
 *
 * @see {@link processPatientCreate} for handling new patient creation
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 * @see {@link generatePatientBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function processPatientUpdate(
	payload: GetCCPatientType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; patientId?: string }> {
	try {
		console.log(`Processing patient update for CC ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessPatientUpdate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Patient update recently processed, skipping. CC ID: ${payload.id}`,
			};
		}

		// Validate required data
		if (!payload?.email && !payload?.phoneMobile) {
			const message = `Email and phone are empty, dropping update patient request. CC ID: ${payload?.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Use smart patient upsert logic (equivalent to v3Integration's Contact.searchCreateOrUpdate)
		const dbPatient = await searchCreateOrUpdatePatient({
			ccId: payload.id,
			ccData: payload,
			email: payload.email || undefined,
			phone: payload.phoneMobile || undefined,
			source: "cc",
		});

		// NEW: Determine sync action based on context-aware logic
		const syncContext = determinePatientSyncAction(dbPatient, "cc", "update");
		console.log(
			`🎯 Context-aware sync decision: ${syncContext.action} - ${syncContext.reason}`,
		);

		// Execute context-aware patient sync
		console.log(
			`🔄 Executing context-aware patient sync for CC ID: ${payload.id}`,
		);
		const syncResult = await executePatientSync(
			dbPatient,
			payload,
			syncContext.action,
			"ap",
		);
		console.log(`🔄 Context-aware sync result:`, syncResult);

		// Handle sync result
		if (syncResult.success) {
			console.log(
				`Patient sync completed success			"ap",
		);
		console.log(`🔄 Context-aware sync result:`, syncResult);

		// Handle sync result
		if (syncResult.success) {
			console.log(
				`Patient sync completed successfully. CC ID: ${payload.id}, Result: ${syncResult.message}`,
			);

			return {
				success: true,
				message: syncResult.message,
				patientId: syncResult.recordId?.toString(),
			};
		} else {
			await logSyncError(
				"PATIENT_UPDATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"CC",
				"PatientProcessor",
			);

			return {
				success: false,
				message: `Failed to sync patient to AP: ${syncResult.message}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"PATIENT_UPDATE_ERROR",
			error,
			payload.id,
			"CC",
			"PatientProcessor",
		);

		throw error;
	}
}

/**
 * Legacy function - DEPRECATED
 *
 * This function has been replaced by the context-aware sync utilities:
 * - determinePatientSyncAction() for intelligent sync decision logic
 * - executePatientSync() for context-aware sync execution
 *
 * The new approach provides:
 * - Smart sync decisions based on existing database state
 * - Prevention of unnecessary API calls for already-synced records
 * - Search-then-create logic for missing records
 * - Improved performance and data consistency
 *
 * @deprecated Use determinePatientSyncAction() and executePatientSync() instead
 * @see {@link determinePatientSyncAction} for context-aware sync decisions
 * @see {@link executePatientSync} for context-aware sync execution
 */
